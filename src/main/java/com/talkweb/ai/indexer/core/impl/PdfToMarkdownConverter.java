package com.talkweb.ai.indexer.core.impl;

import com.talkweb.ai.indexer.core.*;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.pdfbox.text.TextPosition;

import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * A converter that transforms PDF documents into Markdown format.
 */
public class PdfToMarkdownConverter implements DocumentConverter, Plugin {

    private final PluginMetadata metadata;
    private PluginState state = PluginState.STOPPED;

    /**
     * Initializes the converter with the provided metadata.
     */
    public PdfToMarkdownConverter(PluginMetadata metadata) {
        this.metadata = metadata;
    }

    @Override
    public void init(PluginContext context) {
        state = PluginState.READY;
    }

    @Override
    public void start() {
        state = PluginState.RUNNING;
    }

    @Override
    public void stop() {
        state = PluginState.STOPPED;
    }

    @Override
    public void destroy() {
        state = PluginState.STOPPED;
    }

    @Override
    public PluginMetadata getMetadata() {
        return metadata;
    }

    @Override
    public PluginState getState() {
        return state;
    }

    /**
     * Checks if the converter supports the given file extension.
     */
    @Override
    public boolean supportsExtension(String fileExtension) {
        return "pdf".equalsIgnoreCase(fileExtension);
    }

    /**
     * Converts the provided PDF file to Markdown format.
     */
    @Override
    public ConversionResult convert(File inputFile) throws ConversionException {
        if (inputFile == null || !inputFile.exists() || inputFile.length() == 0) {
            throw new ConversionException("Input file is invalid, empty, or does not exist.");
        }

        Path outputPath = Path.of(inputFile.getParent(), inputFile.getName() + ".md");

        try (PDDocument document = Loader.loadPDF(inputFile)) {
            MarkdownPdfTextStripper stripper = new MarkdownPdfTextStripper();
            String markdown = stripper.getText(document);

            return new ConversionResult(
                    ConversionResult.Status.SUCCESS,
                    inputFile.getPath(),
                    outputPath.toString(),
                    markdown
            );
        } catch (IOException e) {
            throw new ConversionException("Failed to convert PDF to Markdown: " + e.getMessage(), e);
        }
    }

    /**
     * A custom PDF stripper that converts PDF content to Markdown by analyzing text positions and styles.
     */
    private static class MarkdownPdfTextStripper extends PDFTextStripper {
        private final StringBuilder markdown = new StringBuilder();
        private final List<TextLine> pageLines = new ArrayList<>();

        // State for the current line being built
        private StringBuilder currentLineText = new StringBuilder();
        private float currentLineY = -1;
        private float currentLineFontSize = -1;
        private boolean currentLineIsBold = false;

        // Regex for table detection (looks for multiple spaces between words)
        private static final Pattern TABLE_LINE_PATTERN = Pattern.compile(".*\\s{2,}.*");
        // Regex for list item detection (matches numbered or bulleted lists)
        private static final Pattern LIST_ITEM_PATTERN = Pattern.compile("^(\\d+\\.|[*\\-])\\s+.*");
        // Regex for capturing numeric list prefixes for reformatting
        private static final Pattern NUMERIC_LIST_PATTERN = Pattern.compile("^\\d+\\.\\s+");

        public MarkdownPdfTextStripper() throws IOException {
            super();
            setSortByPosition(true); // Crucial for correct line segmentation
        }

        @Override
        public String getText(PDDocument doc) throws IOException {
            markdown.setLength(0); // Reset for new conversion
            super.getText(doc); // This triggers the writeXxx methods and processes the whole document
            return markdown.toString();
        }

        @Override
        protected void writePageStart() throws IOException {
            pageLines.clear();
            resetCurrentLine();
        }

        @Override
        protected void writeString(String text, List<TextPosition> textPositions) throws IOException {
            if (textPositions.isEmpty() || text.trim().isEmpty()) {
                return;
            }

            if (currentLineText.length() == 0) {
                TextPosition firstPosition = textPositions.get(0);
                currentLineY = firstPosition.getY();
                currentLineFontSize = firstPosition.getFontSizeInPt();
                currentLineIsBold = isBold(firstPosition);
            }

            currentLineText.append(text);

            // Update style for the whole line if any part has a different style
            for (TextPosition position : textPositions) {
                currentLineFontSize = Math.max(currentLineFontSize, position.getFontSizeInPt());
                if (isBold(position)) {
                    currentLineIsBold = true;
                }
            }
        }

        @Override
        protected void writeLineSeparator() throws IOException {
            if (currentLineText.length() > 0) {
                pageLines.add(new TextLine(currentLineText.toString(), currentLineFontSize, currentLineY, currentLineIsBold));
            }
            resetCurrentLine();
        }

        @Override
        protected void writePageEnd() throws IOException {
            // Process the last line of the page
            writeLineSeparator();
            // Process all collected lines for the page
            processPageLines();
        }

        private void resetCurrentLine() {
            currentLineText.setLength(0);
            currentLineY = -1;
            currentLineFontSize = -1;
            currentLineIsBold = false;
        }

        /**
         * Analyzes the collected lines from a page and converts them to Markdown syntax.
         */
        private void processPageLines() {
            if (pageLines.isEmpty()) return;

            float bodyFontSize = getBodyFontSize();

            for (int i = 0; i < pageLines.size(); i++) {
                TextLine currentLine = pageLines.get(i);
                String lineText = currentLine.text.trim();

                if (lineText.isEmpty()) continue;

                // Heading detection: based on bold style or larger-than-average font size.
                if (currentLine.isBold || currentLine.fontSize > bodyFontSize * 1.1) {
                    int headingLevel = determineHeadingLevel(currentLine.fontSize, bodyFontSize);
                    markdown.append("#".repeat(headingLevel)).append(" ").append(lineText).append("\n\n");
                    continue;
                }

                // List item detection
                if (isListItem(lineText)) {
                    markdown.append(formatListItem(lineText)).append("\n");
                    continue;
                }

                // Table detection: simple check for multiple spaces, indicating columns.
                if (isTableLine(lineText)) {
                    markdown.append(formatTableLine(lineText)).append("\n");
                    // Add a header separator line after the first table row of a table block.
                    if (i == 0 || !isTableLine(pageLines.get(i - 1).text.trim())) {
                        int columnCount = lineText.split("\\s{2,}", -1).length;
                        markdown.append("|" + " --- |".repeat(columnCount)).append("\n");
                    }
                    continue;
                }

                // Paragraph handling: appends text and adds line/paragraph breaks based on vertical spacing.
                markdown.append(lineText);
                if (i + 1 < pageLines.size()) {
                    TextLine nextLine = pageLines.get(i + 1);
                    // A large vertical gap suggests a new paragraph.
                    if (nextLine.y - currentLine.y > currentLine.fontSize * 1.5) {
                        markdown.append("\n\n");
                    } else {
                        markdown.append(" "); // A small gap is treated as a space.
                    }
                } else {
                    markdown.append("\n\n"); // End of page.
                }
            }
        }

        /**
         * Checks if a line appears to be part of a table.
         */
        private boolean isTableLine(String text) {
            return TABLE_LINE_PATTERN.matcher(text).matches();
        }

        /**
         * Formats a table row into Markdown syntax.
         */
        private String formatTableLine(String text) {
            String[] cells = text.split("\\s{2,}");
            StringBuilder sb = new StringBuilder("|");
            for (String cell : cells) {
                sb.append(" ").append(cell.trim()).append(" |");
            }
            return sb.toString();
        }

        /**
         * Checks if a line is a list item (numbered or bulleted).
         */
        private boolean isListItem(String text) {
            return LIST_ITEM_PATTERN.matcher(text).matches();
        }

        /**
         * Formats a line as a Markdown list item.
         */
        private String formatListItem(String text) {
            // If it's already a bullet point, keep it.
            if (LIST_ITEM_PATTERN.matcher(text).matches() && !NUMERIC_LIST_PATTERN.matcher(text).find()) {
                return text;
            }
            // Convert numbered lists to bullet points for consistency.
            return "- " + NUMERIC_LIST_PATTERN.matcher(text).replaceAll("");
        }

        /**
         * Determines the heading level (1, 2, or 3) based on font size relative to the average.
         */
        private int determineHeadingLevel(float fontSize, float bodySize) {
            if (fontSize > bodySize * 1.4) return 1; // Significantly larger
            if (fontSize > bodySize * 1.2) return 2; // Moderately larger
            return 3; // Slightly larger or bold
        }

        /**
         * Calculates the most common font size for the current page to use as the body font size.
         */
        private float getBodyFontSize() {
            if (pageLines.isEmpty()) return 12f; // Default font size

            Map<Float, Long> counts = pageLines.stream()
                    .collect(Collectors.groupingBy(TextLine::getFontSize, Collectors.counting()));

            long maxCount = counts.values().stream().max(Long::compareTo).orElse(0L);

            return counts.entrySet().stream()
                    .filter(entry -> entry.getValue() == maxCount)
                    .map(Map.Entry::getKey)
                    .min(Float::compareTo)
                    .orElse(12.0f);
        }

        /**
         * Checks if the font is bold.
         */
        private boolean isBold(TextPosition text) {
            return text.getFont().getName().toLowerCase().contains("bold");
        }

        /**
         * A simple data structure to hold text along with its essential style and position information.
         */
        private static class TextLine {
            final String text;
            final float fontSize;
            final float y;
            final boolean isBold;

            TextLine(String text, float fontSize, float y, boolean isBold) {
                this.text = text;
                this.fontSize = fontSize;
                this.y = y;
                this.isBold = isBold;
            }

            public float getFontSize() {
                return fontSize;
            }
        }
    }
}
