
package com.talkweb.ai.indexer.core.impl;

import com.talkweb.ai.indexer.core.ConversionException;
import com.talkweb.ai.indexer.core.PluginContext;
import com.talkweb.ai.indexer.core.PluginMetadata;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.Mock;

import java.io.File;
import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.*;

class PdfToMarkdownConverterTest {

    private PdfToMarkdownConverter converter;
    @TempDir Path tempDir;
    @Mock PluginContext context;

    @BeforeEach
    void setUp() {
        PluginMetadata metadata = new PluginMetadata.Builder()
            .id("pdf-converter")
            .name("PDF to Markdown Converter")
            .version("1.0")
            .className("com.talkweb.ai.indexer.core.impl.PdfToMarkdownConverter")
            .build();
        converter = new PdfToMarkdownConverter(metadata);
    }

    @Test
    void shouldSupportPdfFiles() {
        assertTrue(converter.supportsExtension("pdf"));
        assertTrue(converter.supportsExtension("PDF"));
        assertFalse(converter.supportsExtension("docx"));
    }

    @Test
    void shouldConvertBasicText() throws Exception {
        File pdfFile = PdfTestUtils.createSimplePdf(tempDir, "test.pdf");
        var result = converter.convert(pdfFile);

        assertNotNull(result);
        assertTrue(result.getMessage().contains(pdfFile.getPath() + ".md"));
        assertTrue(result.getContent().contains("Test PDF Document"));
    }

    @Test
    void shouldHandleInvalidPdf() {
        File invalidFile = tempDir.resolve("invalid.pdf").toFile();
        assertThrows(ConversionException.class, () -> converter.convert(invalidFile));
    }

    @Test
    void shouldConvertHeadings() throws Exception {
        File pdfFile = PdfTestUtils.createPdfWithHeadings(tempDir);
        var result = converter.convert(pdfFile);

        String content = result.getContent();
        // Check if content contains any text from the PDF
        assertTrue(content.contains("Main Heading") || content.contains("Subheading") || content.contains("Content"));
    }

    @Test
    void shouldPreserveParagraphs() throws Exception {
        File pdfFile = PdfTestUtils.createSimplePdf(tempDir, "paragraphs.pdf");
        var result = converter.convert(pdfFile);

        String content = result.getContent();
        assertTrue(content.contains("\n\n")); // 验证段落分隔
    }

    @Test
    void shouldHandleEmptyPdf() {
        assertThrows(ConversionException.class,
            () -> converter.convert(tempDir.resolve("empty.pdf").toFile()));
    }

    @Test
    void shouldConvertTables() throws Exception {
        File pdfFile = PdfTestUtils.createPdfWithTable(tempDir);
        var result = converter.convert(pdfFile);

        String content = result.getContent();
        // Check if content contains any text from the table
        assertTrue(content.contains("Header 1") && content.contains("Header 2"));
        assertTrue(content.contains("Cell 1") && content.contains("Cell 2"));
    }

    @Test
    void shouldConvertComplexHeadings() throws Exception {
        File pdfFile = PdfTestUtils.createPdfWithComplexHeadings(tempDir);
        var result = converter.convert(pdfFile);

        String content = result.getContent();
        // Check if content contains any text from the headings
        assertTrue(content.contains("Section 3.1"));
        assertTrue(content.contains("Subsection"));
    }

    @Test
    void shouldConvertNumberedLists() throws Exception {
        File pdfFile = PdfTestUtils.createPdfWithLists(tempDir);
        var result = converter.convert(pdfFile);

        String content = result.getContent();
        // Check if content contains any text from the lists
        assertTrue(content.contains("First item"));
        assertTrue(content.contains("Second item"));
    }
}
